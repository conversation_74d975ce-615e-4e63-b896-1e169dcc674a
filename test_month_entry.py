#!/usr/bin/env python3
"""
Тестовый скрипт для проверки функционала входных тестов месяца
"""

import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories.month_test_repository import MonthTestRepository
from database.repositories.student_repository import StudentRepository
from database.repositories.user_repository import UserRepository
from database.repositories.subject_repository import SubjectRepository
from database.repositories.question_repository import QuestionRepository
from common.student_tests.handlers import generate_month_test_questions


async def test_month_entry_functionality():
    """Тестируем функционал входных тестов месяца"""
    print("🧪 Тестирование функционала входных тестов месяца...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Получаем все тесты месяца
        month_tests = await MonthTestRepository.get_all()
        print(f"📋 Найдено тестов месяца: {len(month_tests)}")
        
        for test in month_tests:
            print(f"  - {test.name} ({test.test_type}) - {test.subject.name} - {test.course.name}")
            print(f"    Микротемы: {[mt.microtopic_number for mt in test.microtopics]}")
        
        # Тестируем генерацию вопросов для первого входного теста
        entry_tests = [t for t in month_tests if t.test_type == 'entry']
        if entry_tests:
            test = entry_tests[0]
            print(f"\n🔍 Тестируем генерацию вопросов для теста: {test.name}")
            
            # Получаем вопросы по предмету
            all_questions = await QuestionRepository.get_by_subject(test.subject_id)
            print(f"📝 Всего вопросов по предмету {test.subject.name}: {len(all_questions)}")
            
            # Группируем по микротемам
            questions_by_microtopic = {}
            for question in all_questions:
                if question.microtopic_number:
                    if question.microtopic_number not in questions_by_microtopic:
                        questions_by_microtopic[question.microtopic_number] = []
                    questions_by_microtopic[question.microtopic_number].append(question)
            
            print(f"📊 Вопросы по микротемам:")
            for microtopic_num, questions in questions_by_microtopic.items():
                print(f"  - Микротема {microtopic_num}: {len(questions)} вопросов")
            
            # Генерируем вопросы для теста
            test_questions = await generate_month_test_questions(test.id)
            print(f"🎯 Сгенерировано вопросов для теста: {len(test_questions)}")
            
            for i, question in enumerate(test_questions[:5], 1):  # Показываем первые 5
                print(f"  {i}. Микротема {question['microtopic_number']}: {question['text'][:50]}...")
        
        # Тестируем получение студентов
        students = await StudentRepository.get_all()
        print(f"\n👥 Найдено студентов: {len(students)}")
        
        if students:
            student = students[0]
            print(f"🎓 Тестовый студент: {student.user.name}")
            
            # Получаем курсы студента
            courses = await StudentRepository.get_courses(student.id)
            print(f"📚 Курсы студента: {[c.name for c in courses]}")
            
            # Получаем предметы студента
            subjects = await SubjectRepository.get_by_user_id(student.user_id)
            print(f"📖 Предметы студента: {[s.name for s in subjects]}")
        
        print("\n✅ Тестирование завершено успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_month_entry_functionality())
