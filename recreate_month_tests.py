#!/usr/bin/env python3
"""
Скрипт для пересоздания тестов месяца с микротемами
"""

import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories.month_test_repository import MonthTestRepository
from database.repositories.subject_repository import SubjectRepository
from initialization.month_tests import add_test_month_tests


async def recreate_month_tests():
    """Пересоздаем тесты месяца с микротемами"""
    print("🔄 Пересоздание тестов месяца...")
    
    try:
        # Инициализируем базу данных
        await init_database()
        print("✅ База данных инициализирована")
        
        # Удаляем все существующие тесты месяца
        all_tests = await MonthTestRepository.get_all()
        print(f"📋 Найдено тестов для удаления: {len(all_tests)}")
        
        for test in all_tests:
            await MonthTestRepository.delete(test.id)
            print(f"  🗑️ Удален тест: {test.name}")
        
        # Получаем предметы для создания тестов
        subjects = await SubjectRepository.get_all()
        created_subjects = {subject.name: subject for subject in subjects}
        print(f"📖 Найдено предметов: {len(created_subjects)}")
        
        # Создаем новые тесты с микротемами
        await add_test_month_tests(created_subjects)
        
        # Проверяем результат
        new_tests = await MonthTestRepository.get_all()
        print(f"\n✅ Создано новых тестов: {len(new_tests)}")
        
        for test in new_tests:
            microtopic_numbers = [mt.microtopic_number for mt in test.microtopics]
            print(f"  - {test.name} ({test.subject.name}): микротемы {microtopic_numbers}")
        
        print("\n🎉 Пересоздание тестов месяца завершено успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при пересоздании тестов: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(recreate_month_tests())
